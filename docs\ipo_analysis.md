# IPO Analysis Chart
## Whispers of Eldoria - Input, Process, Output Analysis

### Overview
This document provides a comprehensive Input-Process-Output (IPO) analysis for the Whispers of Eldoria text-based adventure game, detailing all data flows and system interactions.

---

## INPUT ANALYSIS

### Primary User Inputs

| Input Type | Description | Format | Validation Required |
|------------|-------------|---------|-------------------|
| **Menu Selections** | Player choices at decision points | Integer (1-4) | Range validation, type checking |
| **Yes/No Responses** | Confirmation prompts | String ('y', 'n', 'yes', 'no') | Case-insensitive validation |
| **Player Name** | Character identification | String (1-20 characters) | Length validation, character filtering |
| **Restart Commands** | Game replay requests | String ('restart', 'play again') | Keyword recognition |
| **Exit Commands** | Game termination | String ('quit', 'exit') | Keyword recognition |

### Secondary User Inputs

| Input Type | Description | Format | Validation Required |
|------------|-------------|---------|-------------------|
| **Help Requests** | Game assistance | String ('help', '?') | Keyword recognition |
| **Invalid Entries** | Unrecognized input | Any format | Error handling required |
| **Empty Input** | No user response | Empty string | Default handling |

### System Inputs

| Input Type | Description | Source | Usage |
|------------|-------------|---------|-------|
| **Game State** | Current story position | Internal variables | Story progression tracking |
| **Choice History** | Previous player decisions | Internal list/dict | Ending determination |
| **Configuration** | Game settings | Internal constants | Story text and options |

---

## PROCESS ANALYSIS

### Core Game Processes

| Process Name | Description | Inputs Used | Logic Type |
|--------------|-------------|-------------|------------|
| **Game Initialization** | Setup game state and welcome | None | Sequential |
| **Story Display** | Present narrative text | Game state, story data | Conditional |
| **Choice Presentation** | Show available options | Current scene, game state | Conditional |
| **Input Validation** | Verify user input correctness | User input, valid options | Conditional |
| **Decision Processing** | Handle player choice | Validated input, game state | Conditional/Branching |
| **State Management** | Update game progress | Player choice, current state | Sequential |
| **Ending Determination** | Calculate final outcome | Choice history, current path | Conditional |
| **Game Restart** | Reset for new playthrough | Restart command | Sequential |

### Input Validation Processes

| Validation Type | Process Description | Error Handling |
|----------------|-------------------|----------------|
| **Type Checking** | Ensure input matches expected type | Display type error message |
| **Range Validation** | Verify numeric input within bounds | Show valid range options |
| **Format Validation** | Check string format compliance | Provide format examples |
| **Keyword Recognition** | Match input to valid commands | Suggest similar valid options |
| **Length Validation** | Ensure string length within limits | Display character limits |

### Story Logic Processes

| Process Name | Description | Decision Criteria |
|--------------|-------------|------------------|
| **Path Branching** | Determine next story segment | Current choice + game state |
| **Consequence Tracking** | Record choice impacts | Choice type + story context |
| **Character Development** | Update player attributes | Cumulative choice effects |
| **Ending Selection** | Choose appropriate conclusion | Complete choice history |

### Error Handling Processes

| Error Type | Process Description | Recovery Action |
|------------|-------------------|----------------|
| **Invalid Choice** | Handle out-of-range selections | Re-prompt with valid options |
| **Type Mismatch** | Handle non-numeric input | Request correct input type |
| **Empty Input** | Handle no user response | Provide default or re-prompt |
| **System Error** | Handle unexpected failures | Graceful error message + exit |

---

## OUTPUT ANALYSIS

### Primary Game Outputs

| Output Type | Description | Format | Trigger |
|-------------|-------------|---------|---------|
| **Story Text** | Narrative descriptions | Multi-line formatted text | Scene transitions |
| **Choice Menus** | Available player options | Numbered list format | Decision points |
| **Prompts** | User input requests | Single line with indicator | Input required |
| **Feedback Messages** | Choice confirmations | Brief descriptive text | After valid input |
| **Ending Text** | Final story conclusions | Multi-line formatted text | Game completion |

### System Feedback Outputs

| Output Type | Description | Format | Trigger |
|-------------|-------------|---------|---------|
| **Error Messages** | Input validation failures | Single line error text | Invalid input |
| **Help Text** | Game instructions | Multi-line formatted text | Help request |
| **Status Updates** | Game progress indicators | Brief status text | State changes |
| **Confirmation Prompts** | Action verification requests | Yes/No question format | Critical actions |

### Formatting Outputs

| Output Type | Description | Format | Purpose |
|-------------|-------------|---------|---------|
| **Dividers** | Section separators | ASCII art lines | Visual organization |
| **Headers** | Section titles | Centered/emphasized text | Content organization |
| **Spacing** | Visual breaks | Empty lines | Readability improvement |
| **Emphasis** | Important text highlighting | Capitalization/symbols | Attention drawing |

---

## DATA FLOW MAPPING

### Main Game Loop Flow
```
Input: User starts game
Process: Initialize game state
Output: Welcome message + first story segment

Input: User makes choice
Process: Validate input → Update state → Determine next scene
Output: Story continuation + new choices

Input: User reaches ending
Process: Calculate final outcome based on choice history
Output: Ending narrative + restart option
```

### Error Handling Flow
```
Input: Invalid user input
Process: Identify error type → Generate appropriate message
Output: Error explanation + re-prompt for valid input

Input: System error
Process: Log error → Generate user-friendly message
Output: Graceful error message + safe exit option
```

### Restart Flow
```
Input: Restart command
Process: Reset all game state variables → Clear choice history
Output: Fresh game start with welcome message
```

---

## VALIDATION MATRIX

| Input Category | Validation Rules | Error Response | Success Action |
|----------------|------------------|----------------|----------------|
| **Numeric Choices** | Integer, within range 1-N | "Please enter a number between 1 and N" | Process choice |
| **Text Commands** | Non-empty, recognized keywords | "Invalid command. Type 'help' for options" | Execute command |
| **Player Name** | 1-20 chars, alphanumeric + spaces | "Name must be 1-20 characters" | Store name |
| **Yes/No** | 'y', 'n', 'yes', 'no' (case-insensitive) | "Please enter 'y' for yes or 'n' for no" | Process response |

---

## PERFORMANCE CONSIDERATIONS

### Input Processing
- **Response Time:** All input validation should complete within 100ms
- **Memory Usage:** Input buffers should not exceed 1KB per entry
- **Error Recovery:** Maximum 3 retry attempts before offering help

### Output Generation
- **Display Speed:** Text output should appear immediately (no artificial delays)
- **Memory Efficiency:** Story text loaded on-demand, not pre-loaded
- **Formatting Consistency:** All output follows standardized formatting rules

This IPO analysis ensures comprehensive understanding of all data flows within the Whispers of Eldoria game system, providing a solid foundation for implementation and testing phases.
