![][image1]

**Assessment type:** Project **Conditions:**   
**COMPUTER SCIENCE (GECSC)** 

**General Year 11 TASK 5 \- UNIT 2**

**Period allowed for completion of the task:** Week 6 to Week 8 (18 Periods) 

**Date of Submission:** 15-August-2025 

**Late Submission:** 10% deduction from the Total Marks within 3 school days from Date of  submission. Or Zero (0) marks from 4th school day from Date of Submission. 

**Task weighting:** 30% of the school marks for this pair of units  **Name:**  

**Task 5:** Using the stages of the software development cycle (SDC), develop a simple text-based choose your-own-adventure game using a chosen programming language that includes the use of variables,  data types and control structures. 

**Scenario:** 

**State the problem, plan and design, develop the solution, test the solution, evaluate the solution** are  stages in Software Development Cycle. You are to create a text-based choose-your-own adventure game  using Python programming language. The knowledge and skills you have gained in programming  including variables, data types and control structures should help you in creating your version of a  challenging yet engaging version of text based adventure game. 

**What you need to do:** 

**Note:** Develop Task 5 Report using A4 page, using Poppins font, size 9\. The order of the content should be: • Problem Statement 

• Plan and Design 

• Develop the Solution (adventure game code in printed) 

• Testing the Solution (at least five images) 

• Evaluation  

**1\. Problem statement**. State the game title. In one paragraph (two to five sentences long) describe  its objectives, to whom this game is intended for, and requirements to develop the game.  **(3 marks)** 

*Computer Science: Unit Package—Unit 2B-GECSC* 1   
**2\. Plan and Design**  

Before writing your program, you need to decide exactly what it is going to do. A good starting point is to decide what the inputs are, what processing needs to take place and what the outputs  are going to be.  

**A. To help with this process, complete the IPO chart below. Each column should contain the  following information: (6 marks)** • Input 

▪ How will the user interact with the program?  

▪ Any other inputs to the program? 

• Process  

▪ What happens when the user interacts with the program?  

▪ What actions does the program have to perform? 

• Output 

▪ What information does the program give the user? 

▪ What other feedback does the user receive? 

| Input  | Process  | Output |
| ----- | ----- | ----- |
|  |  |  |
|  |  |  |
|  |  |  |

**B. Develop a flowchart based on 2.A. (3 marks)** 

**3\. Develop the Solution (13 marks)** Write your program using the Python programming language, making sure that you refer to your  IPO chart and include all the necessary parts of the program. Remember that your program will  need to: 

• create an appropriate algorithm in the form of pseudocode 

• use variables 

• use a variety of control structures, including sequence, selection and repetition • **Adventure game code** 

**4\. Testing the Solution (2 marks)**  Five (5) images at least showing your working game application using test data. 

**5\. Evaluate the Solution (5 marks)** Once you have finished your game you need to evaluate its effectiveness and see where the game  can be improved. To do this, answer the following questions. 

1\. How effective was your completed adventure game?  

2\. Did it match planning and design that you developed? Why? 

3\. What are the basic hardware and software requirements for the game to run?  4\. How would you improve your adventure game software? 

5\. How does software development cycle stages affects the creation of this game? 6\. **Submit Yourname\_Task5\_AdventureGame.py code. (2 marks)**

| What needs to be submitted for assessment  | Due date |
| :---- | ----- |
| □ Task 5 Report |  |
| □ Yourname\_Task5\_AdventureGame.py code in Google Classroom |  |

*Computer Science: Unit Package—Unit 2B-GECSC* 2   
Name: …………………………………………………………………………………………………………….. Marks: ………………………………………… **Unit 2 \- Task 5** 

**Marking key**  

| Item  | Description  | Possible   mark | Actual   mark |
| ----- | ----- | ----- | ----- |
| Problem   Statement  (3 marks) | 3 \- showing highly relevant definition, requirements and scope  2- showing moderately relevant definition, requirements and scope 1 \- showing attempt to present problem statement | 3 |  |
| Plan and   Design  (9 marks) | IPO chart  **Input \-** *2 marks for suitable input, 1 mark for partial input* **Process \-** *2 marks for suitable process, 1 mark for partial process* **Output \-** *2 marks for suitable output, 1 mark for partial output* | 6 |  |
|  | Flowchart  3 \- showing highly relevant connection/relationship to IPO  2- showing moderately relevant connection/relationship to IPO 1 \- showing attempt to present Flowchart | 3 |  |
| Develop the  Solution  (13 marks) | Use of variables  *2 marks for suitable use of variables (including naming of  variables)  1 mark for limited use of variables* | 2 |  |
|  | Use of correct control structures \- sequence, loops and decisions *Each correct control structure type:  2 marks suitable use  1 mark limited use* | 6 |  |
|  | Operation of game  *2 marks if fault free, smooth operation of game  1 mark if game does not operate smoothly* | 2 |  |
|  | Effectiveness of algorithm  *3 marks for an effective and well-designed algorithm 2 marks for a partially correct algorithm  1 mark for a limited attempt at creating an algorithm* | 3 |  |
| Test   Evidence  (2 marks) | Images showing test stage (five images showing input and ouput  using test data)   2 marks for complete, 1 mark for incomplete | 2 |  |
| Evaluation  (5 marks)  Game code (2 marks) | Effectiveness of completed program  *1 mark for suitable explanation, 0 mark for irrelevant explanation*  | 1 |  |
|  | Matching the IPO chart to the finished product  *1 mark for suitable explanation, 0 mark for irrelevant explanation*  | 1 |  |
|  | Hardware and software requirements  *1 mark for suitable explanation, 0 mark for irrelevant explanation*  | 1 |  |
|  | Game program improvements   *1 mark for suitable explanation, 0 mark for irrelevant explanation*  | 1 |  |
|  | SDC impact/implications on game development   *1 mark for suitable explanation, 0 mark for irrelevant explanation*  | 1 |  |
|  | Game code in Python  | 2 |  |
|  |  **Total**  |  | **/34** |

**Teacher comments** 

… … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … …. … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … … …. … … … 

*Computer Science: Unit Package—Unit 2B-GECSC* 3   
**Links:** 

Choose Your Own Adventure Game in Python (Beginners)  

https://youtu.be/DEcFCn2ubSg?feature=shared 

Text Based Dungeon Game in Python | Coding Tutorial 

https://youtu.be/lI6S2-icPHE?feature=shared 

How To Code A Python Text-Based Adventure Game In 11 Minutes | Programming Tutorial For Beginners https://youtu.be/ORsJn-71\_\_0?feature=shared 

Python AI Choose Your Own Adventure Game \- Tutorial 

https://youtu.be/nhYcTh6vw9A?feature=shared 

**References:** 

1\. Tech With Tim. (2018, December 25). *Choose your own adventure game in Python (Beginners)* \[Video\]. YouTube. https://www.youtube.com/watch?v=DEcFCn2ubSg 

2\. Dante Lee. (2022, August 25). *Text based Dungeon game in Python | Coding Tutorial* \[Video\].  YouTube. https://www.youtube.com/watch?v=lI6S2-icPHE 

3\. Shaun Halverson. (2022, December 30). How to code a Python Text-Based adventure game in 11  minutes | Programming tutorial for beginners \[Video\]. YouTube.  

https://www.youtube.com/watch?v=ORsJn-71\_\_0 

4\. Tech With Tim. (2023b, September 28). *Python AI Choose your own adventure game \- tutorial* \[Video\]. YouTube. https://www.youtube.com/watch?v=nhYcTh6vw9A

*Computer Science: Unit Package—Unit 2B-GECSC* 4 

[image1]: <data:image/png;base64,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>