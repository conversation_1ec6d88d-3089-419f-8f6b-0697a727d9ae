#!/usr/bin/env python3
"""
Whispers of Eldoria - Demo Script

This script demonstrates the game functionality by simulating a complete
playthrough with predefined choices, showcasing all major features and
one possible ending scenario.

Author: Software Development Cycle Demonstration
Version: 1.0
Python Version: 3.8+
"""

import sys
import time
from whispers_of_eldoria import WhispersOfEldoria, GameState


def simulate_user_input(game, inputs):
    """
    Simulate user inputs for demonstration purposes.
    
    Args:
        game: WhispersOfEldoria instance
        inputs: List of inputs to simulate
    """
    input_index = 0
    
    def mock_input(prompt):
        nonlocal input_index
        if input_index < len(inputs):
            response = inputs[input_index]
            input_index += 1
            print(f"{prompt}{response}")
            time.sleep(1)  # Simulate user thinking time
            return response
        return "1"  # Default response
    
    return mock_input


def demo_complete_playthrough():
    """
    Demonstrate a complete game playthrough leading to the Arcane Master ending.
    """
    print("=" * 70)
    print("WHISPERS OF ELDORIA - AUTOMATED DEMO PLAYTHROUGH")
    print("=" * 70)
    print()
    print("This demo will show a complete playthrough of the game,")
    print("demonstrating all major features and leading to the")
    print("'Arcane Master' ending through magic-focused choices.")
    print()
    input("Press Enter to start the demo...")
    print()
    
    # Create game instance
    game = WhispersOfEldoria()
    
    # Define the sequence of inputs for this demo
    demo_inputs = [
        "Eldara",  # Player name
        "1",       # Choice 1: Investigate glowing mushrooms (magic path)
        "1",       # Choice 2: Touch the glowing crystal (magic choice)
        "1",       # Choice 3: Any choice to complete the story
        "1",       # Final confrontation choice
        "n"        # Don't play again
    ]
    
    # Patch the input function to use our simulated inputs
    original_input = __builtins__['input']
    __builtins__['input'] = simulate_user_input(game, demo_inputs)
    
    try:
        # Run the game
        game.run()
    finally:
        # Restore original input function
        __builtins__['input'] = original_input
    
    print()
    print("=" * 70)
    print("DEMO COMPLETED")
    print("=" * 70)
    print()
    print("The demo has shown:")
    print("✓ Game initialization and welcome screen")
    print("✓ Player name input and validation")
    print("✓ Story progression through multiple scenes")
    print("✓ Choice processing and character trait development")
    print("✓ Magic item acquisition and state management")
    print("✓ Ending determination based on player choices")
    print("✓ Complete game conclusion with Arcane Master ending")
    print("✓ Replay prompt and graceful exit")
    print()


def demo_input_validation():
    """
    Demonstrate the input validation features of the game.
    """
    print("=" * 70)
    print("INPUT VALIDATION DEMONSTRATION")
    print("=" * 70)
    print()
    
    game = WhispersOfEldoria()
    
    print("Testing name validation:")
    print("-" * 30)
    
    # Test various name inputs
    test_names = [
        ("", "Empty string"),
        ("A" * 21, "Too long (21 characters)"),
        ("Test@Player", "Invalid characters"),
        ("Valid Name", "Valid name"),
        ("X", "Minimum length"),
        ("A" * 20, "Maximum length")
    ]
    
    for name, description in test_names:
        result = game.validate_name(name)
        status = "✓ VALID" if result else "✗ INVALID"
        print(f"{status}: {description} - '{name}'")
    
    print()
    print("Testing choice validation:")
    print("-" * 30)
    
    # Test various choice inputs
    test_choices = [
        ("1", 3, "Valid choice 1"),
        ("3", 3, "Valid choice 3"),
        ("0", 3, "Below range"),
        ("4", 3, "Above range"),
        ("abc", 3, "Non-numeric"),
        ("", 3, "Empty input"),
        ("1.5", 3, "Decimal number")
    ]
    
    for choice, max_val, description in test_choices:
        result = game.validate_choice_input(choice, max_val)
        status = "✓ VALID" if result else "✗ INVALID"
        print(f"{status}: {description} - '{choice}' (max: {max_val})")
    
    print()


def demo_ending_scenarios():
    """
    Demonstrate how different choices lead to different endings.
    """
    print("=" * 70)
    print("ENDING SCENARIOS DEMONSTRATION")
    print("=" * 70)
    print()
    
    # Test different ending scenarios
    scenarios = [
        {
            "name": "Arcane Master",
            "choices": [1, 1, 1],
            "trait": "intuitive",
            "magic_item": True,
            "description": "Magic-focused choices with intuitive trait and magic item"
        },
        {
            "name": "Wise Scholar", 
            "choices": [2, 2, 2],
            "trait": "observant",
            "magic_item": False,
            "description": "Wisdom-focused choices with observant trait"
        },
        {
            "name": "Brave Hero",
            "choices": [4, 3, 3],
            "trait": "bold", 
            "magic_item": False,
            "description": "Courage-focused choices with bold trait"
        },
        {
            "name": "Cautious Survivor",
            "choices": [],
            "trait": "",
            "magic_item": False,
            "description": "Minimal choices leading to default ending"
        }
    ]
    
    for scenario in scenarios:
        print(f"Scenario: {scenario['name']}")
        print(f"Description: {scenario['description']}")
        
        # Set up game state
        game = WhispersOfEldoria()
        game.game_state.choices_made = scenario['choices']
        game.game_state.character_trait = scenario['trait']
        game.game_state.has_magic_item = scenario['magic_item']
        
        # Determine ending
        game.determine_ending_type()
        
        print(f"Result: {game.game_state.ending_type}")
        print(f"Choices: {scenario['choices']}")
        print(f"Trait: {scenario['trait'] or 'None'}")
        print(f"Magic Item: {scenario['magic_item']}")
        print("-" * 50)
    
    print()


def demo_game_features():
    """
    Demonstrate key game features and technical implementation.
    """
    print("=" * 70)
    print("GAME FEATURES DEMONSTRATION")
    print("=" * 70)
    print()
    
    game = WhispersOfEldoria()
    
    print("1. Scene Management:")
    print("-" * 20)
    scenes = ["forest_entrance", "ancient_ruins", "mystical_cave", "wizard_tower"]
    for scene in scenes:
        choices = game.get_scene_choices(scene)
        print(f"   {scene}: {len(choices)} choices available")
    
    print()
    print("2. Game State Management:")
    print("-" * 25)
    print(f"   Initial scene: {game.game_state.current_scene}")
    print(f"   Valid scenes: {len(game.valid_scenes)} total")
    print(f"   State validation: {'✓ Passed' if game.validate_game_state() is None else '✗ Failed'}")
    
    print()
    print("3. Error Handling:")
    print("-" * 18)
    print("   ✓ Input validation for all user interactions")
    print("   ✓ Graceful handling of invalid choices")
    print("   ✓ User-friendly error messages")
    print("   ✓ Game state consistency checks")
    
    print()
    print("4. Code Quality Features:")
    print("-" * 24)
    print("   ✓ PEP 8 compliant formatting")
    print("   ✓ Comprehensive docstrings")
    print("   ✓ Type hints throughout")
    print("   ✓ Modular function design")
    print("   ✓ Object-oriented architecture")
    
    print()


def main():
    """
    Main demo function that runs all demonstration scenarios.
    """
    print("🌟 WHISPERS OF ELDORIA - COMPREHENSIVE DEMO 🌟")
    print()
    
    demos = [
        ("Complete Playthrough", demo_complete_playthrough),
        ("Input Validation", demo_input_validation),
        ("Ending Scenarios", demo_ending_scenarios),
        ("Game Features", demo_game_features)
    ]
    
    print("Available demonstrations:")
    for i, (name, _) in enumerate(demos, 1):
        print(f"{i}. {name}")
    print("5. Run All Demos")
    print("6. Exit")
    
    while True:
        try:
            choice = input("\nSelect a demo (1-6): ").strip()
            
            if choice == "6":
                print("Thank you for exploring Whispers of Eldoria!")
                break
            elif choice == "5":
                for name, demo_func in demos:
                    print(f"\n{'='*20} {name.upper()} {'='*20}")
                    demo_func()
                    input("\nPress Enter to continue to next demo...")
                print("\n🎉 All demos completed!")
                break
            elif choice in ["1", "2", "3", "4"]:
                demo_index = int(choice) - 1
                name, demo_func = demos[demo_index]
                print(f"\n{'='*20} {name.upper()} {'='*20}")
                demo_func()
            else:
                print("Invalid choice. Please enter 1-6.")
                
        except (ValueError, KeyboardInterrupt):
            print("\nDemo interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"Demo error: {e}")


if __name__ == "__main__":
    main()
