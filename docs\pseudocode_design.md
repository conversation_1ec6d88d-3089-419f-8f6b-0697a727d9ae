# Pseudocode Design Document
## Whispers of Eldoria - Structured Algorithm Design

### Overview
This document provides detailed pseudocode for the complete Whispers of Eldoria game implementation, showing the logical structure and flow of all major components.

---

## MAIN PROGRAM STRUCTURE

```
PROGRAM WhispersOfEldoria
BEGIN
    CALL display_welcome()
    
    DO
        CALL initialize_game_state()
        CALL get_player_name()
        CALL play_game()
        
        SET play_again = CALL ask_play_again()
    WHILE play_again = TRUE
    
    CALL display_goodbye()
END PROGRAM
```

---

## CORE FUNCTION DEFINITIONS

### Game Initialization and Setup

```
FUNCTION initialize_game_state()
BEGIN
    SET game_state = {
        current_scene: "forest_entrance",
        player_name: "",
        choices_made: [],
        has_magic_item: FALSE,
        character_trait: "",
        ending_type: ""
    }
    RETURN game_state
END FUNCTION

FUNCTION get_player_name()
BEGIN
    DO
        DISPLAY "Enter your character's name (1-20 characters): "
        INPUT player_name
        
        IF validate_name(player_name) = TRUE THEN
            SET game_state.player_name = player_name
            BREAK
        ELSE
            DISPLAY "Invalid name. Please use 1-20 alphanumeric characters."
        END IF
    WHILE TRUE
END FUNCTION

FUNCTION validate_name(name)
BEGIN
    IF length(name) < 1 OR length(name) > 20 THEN
        RETURN FALSE
    END IF
    
    FOR each character IN name
        IF character is not alphanumeric AND character is not space THEN
            RETURN FALSE
        END IF
    END FOR
    
    RETURN TRUE
END FUNCTION
```

### Main Game Loop

```
FUNCTION play_game()
BEGIN
    WHILE game_state.current_scene != "game_over"
        CALL display_scene(game_state.current_scene)
        
        SET available_choices = GET scene_choices(game_state.current_scene)
        CALL display_choices(available_choices)
        
        SET player_choice = CALL get_valid_choice(available_choices)
        CALL process_choice(player_choice)
        
        CALL update_game_state(player_choice)
    END WHILE
    
    CALL display_ending()
END FUNCTION
```

### Input Validation System

```
FUNCTION get_valid_choice(available_choices)
BEGIN
    SET max_choice = length(available_choices)
    
    DO
        DISPLAY "Enter your choice (1-" + max_choice + "): "
        INPUT user_input
        
        IF validate_choice_input(user_input, max_choice) = TRUE THEN
            RETURN convert_to_integer(user_input)
        ELSE
            DISPLAY "Invalid choice. Please enter a number between 1 and " + max_choice
        END IF
    WHILE TRUE
END FUNCTION

FUNCTION validate_choice_input(input, max_value)
BEGIN
    TRY
        SET choice_num = convert_to_integer(input)
        
        IF choice_num >= 1 AND choice_num <= max_value THEN
            RETURN TRUE
        ELSE
            RETURN FALSE
        END IF
    CATCH conversion_error
        RETURN FALSE
    END TRY
END FUNCTION
```

### Scene Management System

```
FUNCTION display_scene(scene_name)
BEGIN
    CALL display_divider()
    
    SWITCH scene_name
        CASE "forest_entrance":
            CALL display_forest_entrance()
        CASE "ancient_ruins":
            CALL display_ancient_ruins()
        CASE "mystical_cave":
            CALL display_mystical_cave()
        CASE "wizard_tower":
            CALL display_wizard_tower()
        CASE "final_confrontation":
            CALL display_final_confrontation()
        DEFAULT:
            DISPLAY "Unknown scene error"
    END SWITCH
END FUNCTION

FUNCTION get_scene_choices(scene_name)
BEGIN
    SWITCH scene_name
        CASE "forest_entrance":
            RETURN [
                "Investigate the glowing mushrooms",
                "Follow the mysterious footprints", 
                "Climb the ancient oak tree",
                "Head toward the distant ruins"
            ]
        CASE "ancient_ruins":
            RETURN [
                "Enter the crumbling temple",
                "Search the overgrown courtyard",
                "Examine the mystical symbols"
            ]
        CASE "mystical_cave":
            RETURN [
                "Touch the glowing crystal",
                "Read the ancient inscriptions",
                "Follow the underground river"
            ]
        CASE "wizard_tower":
            RETURN [
                "Knock on the tower door",
                "Climb the exterior wall",
                "Call out to the wizard"
            ]
        DEFAULT:
            RETURN ["Continue"]
    END SWITCH
END FUNCTION
```

### Choice Processing and State Management

```
FUNCTION process_choice(choice_number)
BEGIN
    SET current_scene = game_state.current_scene
    
    SWITCH current_scene
        CASE "forest_entrance":
            CALL process_forest_choice(choice_number)
        CASE "ancient_ruins":
            CALL process_ruins_choice(choice_number)
        CASE "mystical_cave":
            CALL process_cave_choice(choice_number)
        CASE "wizard_tower":
            CALL process_tower_choice(choice_number)
    END SWITCH
    
    ADD choice_number TO game_state.choices_made
END FUNCTION

FUNCTION process_forest_choice(choice)
BEGIN
    SWITCH choice
        CASE 1: // Glowing mushrooms
            DISPLAY "You discover magical spores that enhance your intuition."
            SET game_state.character_trait = "intuitive"
            SET game_state.current_scene = "mystical_cave"
            
        CASE 2: // Mysterious footprints
            DISPLAY "The footprints lead you to an ancient wizard's tower."
            SET game_state.character_trait = "tracker"
            SET game_state.current_scene = "wizard_tower"
            
        CASE 3: // Ancient oak tree
            DISPLAY "From the treetop, you spot ruins and find a magical amulet."
            SET game_state.has_magic_item = TRUE
            SET game_state.character_trait = "observant"
            SET game_state.current_scene = "ancient_ruins"
            
        CASE 4: // Distant ruins
            DISPLAY "You approach the mysterious ruins directly."
            SET game_state.character_trait = "bold"
            SET game_state.current_scene = "ancient_ruins"
    END SWITCH
END FUNCTION

FUNCTION update_game_state(choice)
BEGIN
    // Check if we should proceed to final confrontation
    IF length(game_state.choices_made) >= 3 THEN
        SET game_state.current_scene = "final_confrontation"
    END IF
    
    // Determine ending type based on choices and traits
    IF game_state.current_scene = "final_confrontation" THEN
        CALL determine_ending_type()
        SET game_state.current_scene = "game_over"
    END IF
END FUNCTION
```

### Ending Determination Logic

```
FUNCTION determine_ending_type()
BEGIN
    SET magic_score = 0
    SET wisdom_score = 0
    SET courage_score = 0
    
    // Analyze choice patterns
    FOR each choice IN game_state.choices_made
        IF choice involves magic or mystical elements THEN
            INCREMENT magic_score
        END IF
        
        IF choice involves careful observation or learning THEN
            INCREMENT wisdom_score
        END IF
        
        IF choice involves direct action or risk-taking THEN
            INCREMENT courage_score
        END IF
    END FOR
    
    // Factor in character traits and items
    IF game_state.character_trait = "intuitive" THEN
        INCREMENT magic_score BY 2
    ELSE IF game_state.character_trait = "observant" THEN
        INCREMENT wisdom_score BY 2
    ELSE IF game_state.character_trait = "bold" THEN
        INCREMENT courage_score BY 2
    END IF
    
    IF game_state.has_magic_item = TRUE THEN
        INCREMENT magic_score BY 1
    END IF
    
    // Determine ending based on highest score
    IF magic_score > wisdom_score AND magic_score > courage_score THEN
        SET game_state.ending_type = "arcane_master"
    ELSE IF wisdom_score > courage_score THEN
        SET game_state.ending_type = "wise_scholar"
    ELSE IF courage_score > 0 THEN
        SET game_state.ending_type = "brave_hero"
    ELSE
        SET game_state.ending_type = "cautious_survivor"
    END IF
END FUNCTION
```

### Display Functions

```
FUNCTION display_welcome()
BEGIN
    CALL clear_screen()
    DISPLAY "╔══════════════════════════════════════════════════════════════╗"
    DISPLAY "║                    WHISPERS OF ELDORIA                       ║"
    DISPLAY "║                A Mystical Text Adventure                     ║"
    DISPLAY "╚══════════════════════════════════════════════════════════════╝"
    DISPLAY ""
    DISPLAY "Welcome to the mystical realm of Eldoria, where ancient magic"
    DISPLAY "flows through enchanted forests and forgotten ruins hold"
    DISPLAY "secrets of power beyond imagination..."
    DISPLAY ""
END FUNCTION

FUNCTION display_divider()
BEGIN
    DISPLAY ""
    DISPLAY "═══════════════════════════════════════════════════════════════"
    DISPLAY ""
END FUNCTION

FUNCTION display_choices(choices)
BEGIN
    DISPLAY ""
    DISPLAY "What do you choose to do?"
    DISPLAY ""
    
    FOR i = 1 TO length(choices)
        DISPLAY i + ". " + choices[i-1]
    END FOR
    DISPLAY ""
END FUNCTION

FUNCTION display_ending()
BEGIN
    CALL display_divider()
    
    SWITCH game_state.ending_type
        CASE "arcane_master":
            CALL display_arcane_master_ending()
        CASE "wise_scholar":
            CALL display_wise_scholar_ending()
        CASE "brave_hero":
            CALL display_brave_hero_ending()
        CASE "cautious_survivor":
            CALL display_cautious_survivor_ending()
    END SWITCH
    
    DISPLAY ""
    DISPLAY "Thank you for playing Whispers of Eldoria!"
END FUNCTION
```

### Utility Functions

```
FUNCTION ask_play_again()
BEGIN
    DO
        DISPLAY ""
        DISPLAY "Would you like to play again? (y/n): "
        INPUT response
        
        SET response = convert_to_lowercase(trim(response))
        
        IF response = "y" OR response = "yes" THEN
            RETURN TRUE
        ELSE IF response = "n" OR response = "no" THEN
            RETURN FALSE
        ELSE
            DISPLAY "Please enter 'y' for yes or 'n' for no."
        END IF
    WHILE TRUE
END FUNCTION

FUNCTION clear_screen()
BEGIN
    // Platform-specific screen clearing
    IF operating_system = "Windows" THEN
        EXECUTE "cls"
    ELSE
        EXECUTE "clear"
    END IF
END FUNCTION

FUNCTION display_goodbye()
BEGIN
    CALL clear_screen()
    DISPLAY "Thank you for exploring the mystical realm of Eldoria!"
    DISPLAY "May your adventures continue in dreams and imagination..."
    DISPLAY ""
    DISPLAY "Game created as part of Software Development Cycle demonstration."
END FUNCTION
```

---

## ERROR HANDLING PSEUDOCODE

```
FUNCTION handle_unexpected_error(error_message)
BEGIN
    DISPLAY "An unexpected error occurred: " + error_message
    DISPLAY "The game will now exit safely."
    DISPLAY "Please restart the program to play again."
    
    LOG error_message TO error_log
    EXIT program WITH error_code 1
END FUNCTION

FUNCTION validate_game_state()
BEGIN
    IF game_state is NULL OR game_state is invalid THEN
        CALL handle_unexpected_error("Invalid game state detected")
    END IF
    
    IF game_state.current_scene is not in valid_scenes THEN
        CALL handle_unexpected_error("Unknown scene: " + game_state.current_scene)
    END IF
END FUNCTION
```

---

## TESTING HOOKS PSEUDOCODE

```
FUNCTION run_automated_tests()
BEGIN
    CALL test_input_validation()
    CALL test_scene_transitions()
    CALL test_ending_determination()
    CALL test_game_state_management()
    
    DISPLAY "All automated tests completed."
END FUNCTION

FUNCTION test_input_validation()
BEGIN
    // Test valid inputs
    ASSERT validate_choice_input("1", 3) = TRUE
    ASSERT validate_choice_input("3", 3) = TRUE
    
    // Test invalid inputs
    ASSERT validate_choice_input("0", 3) = FALSE
    ASSERT validate_choice_input("4", 3) = FALSE
    ASSERT validate_choice_input("abc", 3) = FALSE
    
    DISPLAY "Input validation tests passed."
END FUNCTION
```

This pseudocode provides a complete blueprint for implementing the Whispers of Eldoria game with proper structure, error handling, and testing considerations.
